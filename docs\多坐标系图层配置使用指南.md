# 多坐标系图层配置使用指南

## 概述

本文档介绍了如何使用新的多坐标系图层配置系统，该系统支持天地图（CGCS2000）和高德地图（GCJ02）两种不同的坐标系，并能根据底图类型自动切换对应的业务图层配置。

## 核心配置

### 1. 文件结构

坐标系相关的类型定义现在统一放在 `layer-option.ts` 文件中，便于集中管理：

```
src/app/share/map-component/map/class/layer-option.ts  # 类型定义和枚举
src/app/share/map-component/map/layer.config.ts       # 图层配置数据
```

### 2. 坐标系枚举

在 `layer-option.ts` 中定义：

```typescript
export enum CoordinateSystem {
  WGS84 = 'WGS84',           // WGS84坐标系
  CGCS2000 = 'CGCS2000',     // 天地图坐标系
  GCJ02 = 'GCJ02'            // 高德地图坐标系
}
```

### 3. 底图与坐标系映射

在 `layer-option.ts` 中定义：

```typescript
export const BaseMapCoordinateMapping: { [key: string]: CoordinateSystem } = {
  'td_vec': CoordinateSystem.CGCS2000,    // 天地矢量
  'td_img': CoordinateSystem.CGCS2000,    // 天地影像
  'amap_vec': CoordinateSystem.GCJ02,     // 高德矢量
  'amap_img': CoordinateSystem.GCJ02      // 高德影像
};
```

### 4. 按坐标系分组的业务图层配置

在 `layer.config.ts` 中定义，系统现在支持为不同坐标系配置不同的业务图层参数：

#### 天地图坐标系 (CGCS2000) 配置
- 图层名称：`sx:inspect_point`
- 坐标系：CGCS2000（接近WGS84）
- 无需额外的坐标系参数

#### 高德地图坐标系 (GCJ02) 配置
- 图层名称：`sx:g_inspect_point`
- 坐标系：GCJ02
- 需要添加 `srs: 'EPSG:3857'` 参数

## 关键特性

### 1. 自动坐标系切换

当用户在地图组件中切换底图时，系统会：
1. 自动识别新底图对应的坐标系
2. 切换到对应的业务图层配置
3. 重新加载所有业务图层
4. 保持地图视图状态

### 2. 图层ID匹配机制

系统支持通过逻辑ID匹配图层，忽略坐标系后缀：
- `inspect_point` 可以匹配 `inspect_point_cgcs2000` 或 `inspect_point_gcj02`
- 这确保了现有代码的兼容性

### 3. 向后兼容性

保留了原有的 `BusinessLayerConfigsCN` 导出，确保现有代码不会中断：
```typescript
export const BusinessLayerConfigsCN: LayerOption[] = BusinessLayerConfigs[CoordinateSystem.CGCS2000];
```

## 使用方法

### 1. 在 MapComponent 中使用

MapComponent 已经自动集成了坐标系服务：

```typescript
// 初始化时自动使用当前坐标系的业务图层
private initBusinessLayers(layerIds?: string[]): void {
  const layers = this.coordinateSystemService.getCurrentBusinessLayers(layerIds);
  // ...
}

// 监听坐标系变化，自动重新加载业务图层
ngOnInit(): void {
  this.coordinateSystemService.getCurrentCoordinateSystem()
    .pipe(takeUntil(this.destroy$))
    .subscribe(() => {
      this.reloadBusinessLayers();
    });
}
```

### 2. 在 MapSwitchComponent 中切换底图

底图切换时会自动触发坐标系切换：

```typescript
onMapServe(item: LayerGroup): void {
  const newBaseMapId = item.get('id');
  
  // 切换底图
  this.baseLayer.setLayers(item.getLayers());
  
  // 自动切换坐标系和业务图层
  this.coordinateSystemService.switchBaseMap(newBaseMapId);
}
```

### 3. 手动获取特定坐标系的图层配置

```typescript
import { CoordinateSystem } from '../class/layer-option';
import { BusinessLayerConfigs } from '../layer.config';

// 获取天地图坐标系的业务图层
const cgcs2000Layers = BusinessLayerConfigs[CoordinateSystem.CGCS2000];

// 获取高德地图坐标系的业务图层
const gcj02Layers = BusinessLayerConfigs[CoordinateSystem.GCJ02];

// 获取当前坐标系的业务图层
const currentLayers = this.coordinateSystemService.getCurrentBusinessLayers();
```

## 配置差异对比

| 配置项 | 天地图 (CGCS2000) | 高德地图 (GCJ02) |
|--------|------------------|------------------|
| 巡检点图层名 | `sx:inspect_point` | `sx:g_inspect_point` |
| 坐标系参数 | 无 | `srs: 'EPSG:3857'` |
| 管线图层名 | `sx:p_pipe_joint_info` | `sx:g_p_pipe_joint_info` |
| 摄像头图层名 | `sx:df_camera` | `sx:g_df_camera` |
| 报警信息图层名 | `sx:inspect_alarm` | `sx:g_inspect_alarm` |

## 注意事项

1. **图层命名规范**：高德地图的图层名称统一添加 `g_` 前缀
2. **坐标系参数**：高德地图图层需要添加 `srs: 'EPSG:3857'` 参数
3. **兼容性**：现有使用 `BusinessLayerConfigsCN` 的代码仍然可以正常工作
4. **性能**：坐标系切换时会重新加载所有业务图层，可能有短暂的加载时间

## 扩展支持

如需添加新的坐标系或底图类型：

1. 在 `CoordinateSystem` 枚举中添加新的坐标系
2. 在 `BaseMapCoordinateMapping` 中添加底图与坐标系的映射
3. 在 `BusinessLayerConfigs` 中添加对应的业务图层配置
4. 在 `CoordinateSystemService` 中添加相应的坐标转换逻辑

这样的设计确保了系统的可扩展性和维护性。
