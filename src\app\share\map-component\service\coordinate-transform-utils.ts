import { Coordinate } from 'ol/coordinate';
import { CoordinateSystem } from '../map/layer.config';
import { CoordinateSystemService, CoordinateTransformResult } from './coordinate-system.service';

/**
 * 坐标转换工具类
 * 提供便捷的坐标转换方法和批量处理功能
 */
export class CoordinateTransformUtils {
  
  constructor(private coordinateSystemService: CoordinateSystemService) {}



  /**
   * 批量坐标转换
   * @param coordinates 坐标数组
   * @param targetCRS 目标坐标系
   * @param sourceCRS 源坐标系
   */
  batchTransform(
    coordinates: Coordinate[], 
    targetCRS: CoordinateSystem,
    sourceCRS: CoordinateSystem
  ): CoordinateTransformResult[] {
    return this.coordinateSystemService.batchTransformCoordinates(coordinates, sourceCRS, targetCRS);
  }

  /**
   * 转换关键点数据
   * @param keyPoint 关键点数据
   * @param targetCRS 目标坐标系
   */
  transformKeyPoint(keyPoint: any, targetCRS?: CoordinateSystem): any {
    const finalTargetCRS = targetCRS || this.coordinateSystemService.getCurrentCoordinateSystemValue();
    
    if (keyPoint.pointGeom && Array.isArray(keyPoint.pointGeom)) {
      const sourceCRS = keyPoint.coordinateSystem || CoordinateSystem.CGCS2000; // 默认假设为CGCS2000
      
      if (sourceCRS !== finalTargetCRS) {
        const transformResult = this.coordinateSystemService.transformCoordinate(
          keyPoint.pointGeom,
          sourceCRS,
          finalTargetCRS
        );
        
        return {
          ...keyPoint,
          pointGeom: transformResult.coordinate,
          coordinateSystem: finalTargetCRS,
          originalCoordinate: keyPoint.pointGeom,
          originalCoordinateSystem: sourceCRS,
          transformAccuracy: transformResult.accuracy
        };
      }
    }
    
    return keyPoint;
  }

  /**
   * 转换关键点数组
   * @param keyPoints 关键点数组
   * @param targetCRS 目标坐标系
   */
  transformKeyPoints(keyPoints: any[], targetCRS?: CoordinateSystem): any[] {
    return keyPoints.map(point => this.transformKeyPoint(point, targetCRS));
  }

  /**
   * 检查坐标是否需要转换
   * @param coordinate 坐标
   * @param sourceCRS 源坐标系
   * @param targetCRS 目标坐标系
   */
  needsTransform(coordinate: Coordinate, sourceCRS: CoordinateSystem, targetCRS?: CoordinateSystem): boolean {
    const finalTargetCRS = targetCRS || this.coordinateSystemService.getCurrentCoordinateSystemValue();
    return sourceCRS !== finalTargetCRS && this.coordinateSystemService.isValidCoordinate(coordinate);
  }



  /**
   * 格式化坐标显示
   * @param coordinate 坐标
   * @param precision 精度（小数位数）
   */
  formatCoordinate(coordinate: Coordinate, precision: number = 6): string {
    if (!coordinate || coordinate.length < 2) {
      return '无效坐标';
    }

    const [lng, lat] = coordinate;
    return `${lng.toFixed(precision)}, ${lat.toFixed(precision)}`;
  }
}
