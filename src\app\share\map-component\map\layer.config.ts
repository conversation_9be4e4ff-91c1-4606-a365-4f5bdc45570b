import { environment } from 'src/environments/environment';
import { LayerOption, CoordinateSystem } from './class/layer-option';

// 重新导出类型定义以保持向后兼容性
export { CoordinateSystem, BaseMapCoordinateMapping } from './class/layer-option';

export const LayerConfigsCN: LayerOption[] = [
    {
        id: 'td_vec',
        name: '天地矢量',
        serverType: 'GROUP',
        selected: true,
        icon: 'assets/map/gl_vec.png',
        childs: [
            {
                id: 'td_vec_0',
                // url: `http://t{0-6}.tianditu.com/DataServer?T=vec_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                url: `https://t{0-6}.tianditu.gov.cn/DataServer?T=vec_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                serverType: 'TILE',
            },
            {
                id: 'td_vec_1',
                // url: `http://t{0-6}.tianditu.com/DataServer?T=cva_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                url: `https://t{0-6}.tianditu.gov.cn/DataServer?T=cva_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                serverType: 'TILE',
            }
        ]
    },
    {
        id: 'td_img',
        name: '天地影像',
        serverType: 'GROUP',
        selected: false,
        icon: 'assets/map/gl_img.png',
        childs: [
            {
                id: 'td_img_0',
                // url: `http://t{0-6}.tianditu.com/DataServer?T=img_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                url: `https://t{0-6}.tianditu.gov.cn/DataServer?T=img_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                serverType: 'TILE',
            },
            {
                id: 'td_img1',
                // url: `http://t{0-6}.tianditu.com/DataServer?T=cva_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                url: `https://t{0-6}.tianditu.gov.cn/DataServer?T=cva_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                serverType: 'TILE',
            },
        ]
    },
    {
        id: 'amap_vec', // 图层ID
        name: '高德矢量', // 图层名称
        serverType: 'GROUP', // 图层类型（组）
        selected: false, // 默认是否选中
        icon: 'assets/map/gl_vec.png', // 图层图标（可选）
        childs: [
            {
                id: 'amap_vec_0',
                url: 'https://webrd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}&key=af2dfb6c6340d07e29f469f195acef8f',
                serverType: 'TILE',
            }
        ]
    },
    {
        id: 'amap_img',
        name: '高德影像',
        serverType: 'GROUP',
        selected: false,
        icon: 'assets/map/gl_img.png',
        childs: [
            {
                id: 'amap_img_0',
                url: 'https://webst0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=6&x={x}&y={y}&z={z}&key=af2dfb6c6340d07e29f469f195acef8f',
                serverType: 'TILE',
            },
            {
                id: 'amap_img_1',
                url: 'https://webst0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=7&x={x}&y={y}&z={z}&key=af2dfb6c6340d07e29f469f195acef8f',
                serverType: 'TILE',
            },
        ]
    },
];


/**
 * 按坐标系分组的业务图层配置
 * 注意：只配置实际使用的坐标系（CGCS2000用于天地图，GCJ02用于高德地图）
 */
export const BusinessLayerConfigs: { [key in CoordinateSystem.CGCS2000 | CoordinateSystem.GCJ02]: LayerOption[] } = {
  // 天地图坐标系 (CGCS2000) 业务图层配置
  [CoordinateSystem.CGCS2000]: [
    {
      id: 'p_pipe_joint_info_cgcs2000',
      name: '管线',
      serverType: 'WMS',
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:p_pipe_joint_info', VERSION: '1.1.0', FORMAT: 'image/png' },
    },
    {
      id: 'inspect_point_cgcs2000',
      name: '关键点',
      serverType: 'WMS',
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:inspect_point', VERSION: '1.1.0', FORMAT: 'image/png' },
    },
    {
      id: 'df_camera_cgcs2000',
      name: '摄像头',
      serverType: 'WMS',
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:df_camera', VERSION: '1.1.0', FORMAT: 'image/png' },
    },
    {
      id: 'inspect_alarm_cgcs2000',
      name: '报警信息',
      serverType: 'WMS',
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:inspect_alarm', VERSION: '1.1.0', FORMAT: 'image/png' },
    },
    {
      id: 'inspect_vehicle_cgcs2000',
      name: '巡视（车巡）',
      serverType: 'WMS',
      selected: false,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:inspect_point', VERSION: '1.1.0', FORMAT: 'image/png', CQL_FILTER: "inspectionMethod='巡视'" },
    },
    {
      id: 'inspect_person_cgcs2000',
      name: '巡查（人巡）',
      serverType: 'WMS',
      selected: false,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:inspect_point', VERSION: '1.1.0', FORMAT: 'image/png', CQL_FILTER: "inspectionMethod='巡查'" },
    }
  ],

  // 高德地图坐标系 (GCJ02) 业务图层配置
  [CoordinateSystem.GCJ02]: [
    {
      id: 'p_pipe_joint_info_gcj02',
      name: '管线',
      serverType: 'WMS',
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:g_p_pipe_joint_info', VERSION: '1.1.0', FORMAT: 'image/png', CRS: 'EPSG:3857' },
    },
    {
      id: 'inspect_point_gcj02',
      name: '关键点',
      serverType: 'WMS',
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:g_inspect_point', VERSION: '1.1.0', FORMAT: 'image/png', CRS: 'EPSG:3857' },
    },
    {
      id: 'df_camera_gcj02',
      name: '摄像头',
      serverType: 'WMS',
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:g_df_camera', VERSION: '1.1.0', FORMAT: 'image/png', CRS: 'EPSG:3857' },
    },
    {
      id: 'inspect_alarm_gcj02',
      name: '报警信息',
      serverType: 'WMS',
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:g_inspect_alarm', VERSION: '1.1.0', FORMAT: 'image/png', CRS: 'EPSG:3857' },
    },
    {
      id: 'inspect_vehicle_gcj02',
      name: '巡视（车巡）',
      serverType: 'WMS',
      selected: false,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:g_inspect_point', VERSION: '1.1.0', FORMAT: 'image/png', CRS: 'EPSG:3857', CQL_FILTER: "inspectionMethod='巡视'" },
    },
    {
      id: 'inspect_person_gcj02',
      name: '巡查（人巡）',
      serverType: 'WMS',
      selected: false,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: { LAYERS: 'sx:g_inspect_point', VERSION: '1.1.0', FORMAT: 'image/png', CRS: 'EPSG:3857', CQL_FILTER: "inspectionMethod='巡查'" },
    }
  ]
};

/**
 * 业务图层（保持向后兼容）
 * @deprecated 请使用 BusinessLayerConfigs[CoordinateSystem.CGCS2000] 替代
 */
export const BusinessLayerConfigsCN: LayerOption[] = BusinessLayerConfigs[CoordinateSystem.CGCS2000];
