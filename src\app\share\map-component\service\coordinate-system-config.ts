import { CoordinateSystem } from '../map/layer.config';

/**
 * 坐标系转换配置
 */
export interface CoordinateSystemConfig {
  // 默认坐标系
  defaultCoordinateSystem: CoordinateSystem;
  
  // 是否启用自动坐标转换
  enableAutoTransform: boolean;
  
  // 转换精度阈值（米）
  transformAccuracyThreshold: number;
  
  // 是否启用转换缓存
  enableTransformCache: boolean;
  
  // 缓存大小限制
  maxCacheSize: number;
  
  // 是否启用转换日志
  enableTransformLogging: boolean;
  
  // 坐标有效性检查配置
  coordinateValidation: {
    // 中国境内经度范围
    longitudeRange: [number, number];
    // 中国境内纬度范围
    latitudeRange: [number, number];
    // 是否启用严格验证
    strictValidation: boolean;
  };
}

/**
 * 默认坐标系配置
 */
export const DEFAULT_COORDINATE_SYSTEM_CONFIG: CoordinateSystemConfig = {
  defaultCoordinateSystem: CoordinateSystem.CGCS2000,
  enableAutoTransform: true,
  transformAccuracyThreshold: 10, // 10米
  enableTransformCache: true,
  maxCacheSize: 1000,
  enableTransformLogging: true,
  coordinateValidation: {
    longitudeRange: [73, 135], // 中国境内经度范围
    latitudeRange: [18, 54],   // 中国境内纬度范围
    strictValidation: false
  }
};

/**
 * 坐标系兼容性矩阵
 */
export const COORDINATE_SYSTEM_COMPATIBILITY_MATRIX = {
  [CoordinateSystem.WGS84]: {
    [CoordinateSystem.CGCS2000]: { supported: true, accuracy: 'high', note: '直接兼容，精度极高' },
    [CoordinateSystem.GCJ02]: { supported: true, accuracy: 'high', note: '通过coordtransform库转换' }
  },
  [CoordinateSystem.CGCS2000]: {
    [CoordinateSystem.WGS84]: { supported: true, accuracy: 'high', note: '直接兼容，精度极高' },
    [CoordinateSystem.GCJ02]: { supported: true, accuracy: 'high', note: '通过WGS84中转' }
  },
  [CoordinateSystem.GCJ02]: {
    [CoordinateSystem.WGS84]: { supported: true, accuracy: 'high', note: '通过coordtransform库转换' },
    [CoordinateSystem.CGCS2000]: { supported: true, accuracy: 'high', note: '通过WGS84中转' }
  }
};

/**
 * 获取坐标系转换兼容性信息
 */
export function getCoordinateSystemCompatibility(
  from: CoordinateSystem, 
  to: CoordinateSystem
): { supported: boolean; accuracy: string; note: string } {
  return COORDINATE_SYSTEM_COMPATIBILITY_MATRIX[from]?.[to] || {
    supported: false,
    accuracy: 'unknown',
    note: '不支持的坐标系转换'
  };
}
