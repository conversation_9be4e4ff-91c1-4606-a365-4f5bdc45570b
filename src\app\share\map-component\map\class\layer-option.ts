import { Extent } from 'ol/extent';
import { Options } from 'ol/Tile';

export class LayerOption {
    readonly id: string;
    readonly childs?: LayerOption[];
    readonly name?: string;
    readonly icon?: string;
    readonly url?: string;
    readonly serverType?: ServerType;
    readonly param?: any;
    readonly tileGrid?: Options;
    readonly projection?: string;
    readonly extent?: Extent;
    readonly format?: FormatStr;
    readonly formatOption?: any;
    readonly coordinateSystem?: string; // 新增坐标系字段
    visible?: boolean;
    selected?: boolean;
}
export type ServerType = 'GROUP' | 'TILE' | 'WMS' | 'WFS' | 'KML' | 'WMTS' | 'XYZ' | 'TILE-TILEGRID';
export type FormatStr = 'GEOJSON' | 'GML';

/**
 * 坐标系枚举
 */
export enum CoordinateSystem {
  WGS84 = 'WGS84',           // WGS84坐标系
  CGCS2000 = 'CGCS2000',     // 天地图坐标系
  GCJ02 = 'GCJ02'            // 高德地图坐标系
}

/**
 * 底图与坐标系映射关系
 */
export const BaseMapCoordinateMapping: { [key: string]: CoordinateSystem } = {
  'td_vec': CoordinateSystem.CGCS2000,    // 天地矢量
  'td_img': CoordinateSystem.CGCS2000,    // 天地影像
  'amap_vec': CoordinateSystem.GCJ02,     // 高德矢量
  'amap_img': CoordinateSystem.GCJ02      // 高德影像
};
