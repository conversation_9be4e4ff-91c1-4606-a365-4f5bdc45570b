import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Coordinate } from 'ol/coordinate';
import { BusinessLayerConfigs } from '../map/layer.config';
import { LayerOption, CoordinateSystem, BaseMapCoordinateMapping } from '../map/class/layer-option';
import * as coordtransform from 'coordtransform';

/**
 * 坐标转换结果接口
 */
export interface CoordinateTransformResult {
  coordinate: Coordinate;
  accuracy: number;
  source: CoordinateSystem;
  target: CoordinateSystem;
}

/**
 * 坐标系管理服务
 * 负责坐标系切换、坐标转换、图层配置管理
 */
@Injectable({
  providedIn: 'root'
})
export class CoordinateSystemService {
  // 当前激活的坐标系
  private currentCoordinateSystem$ = new BehaviorSubject<CoordinateSystem>(CoordinateSystem.CGCS2000);
  
  // 当前激活的底图ID
  private currentBaseMapId$ = new BehaviorSubject<string>('td_vec');
  
  // 坐标转换缓存
  private transformCache = new Map<string, CoordinateTransformResult>();
  
  // 缓存最大数量
  private readonly MAX_CACHE_SIZE = 1000;

  constructor() {}

  /**
   * 获取当前坐标系
   */
  getCurrentCoordinateSystem(): Observable<CoordinateSystem> {
    return this.currentCoordinateSystem$.asObservable();
  }

  /**
   * 获取当前坐标系值
   */
  getCurrentCoordinateSystemValue(): CoordinateSystem {
    return this.currentCoordinateSystem$.value;
  }

  /**
   * 获取当前底图ID
   */
  getCurrentBaseMapId(): Observable<string> {
    return this.currentBaseMapId$.asObservable();
  }

  /**
   * 获取当前底图ID值
   */
  getCurrentBaseMapIdValue(): string {
    return this.currentBaseMapId$.value;
  }

  /**
   * 切换底图并更新坐标系
   * @param baseMapId 底图ID
   */
  switchBaseMap(baseMapId: string): void {
    const coordinateSystem = BaseMapCoordinateMapping[baseMapId];
    if (coordinateSystem) {
      this.currentBaseMapId$.next(baseMapId);
      this.currentCoordinateSystem$.next(coordinateSystem);
      console.log(`🗺️ 底图切换: ${baseMapId}, 坐标系: ${coordinateSystem}`);
    }
  }

  /**
   * 根据当前底图获取对应的业务图层配置
   * @param layerIds 可选的图层ID过滤列表
   */
  getCurrentBusinessLayers(layerIds?: string[]): LayerOption[] {
    const currentCRS = this.getCurrentCoordinateSystemValue();

    // 只支持CGCS2000和GCJ02坐标系的业务图层
    let businessLayers: LayerOption[];
    if (currentCRS === CoordinateSystem.CGCS2000 || currentCRS === CoordinateSystem.GCJ02) {
      businessLayers = BusinessLayerConfigs[currentCRS];
    } else {
      // 如果是WGS84或其他坐标系，默认使用CGCS2000配置（因为WGS84与CGCS2000接近）
      businessLayers = BusinessLayerConfigs[CoordinateSystem.CGCS2000];
    }

    if (layerIds && layerIds.length > 0) {
      return businessLayers.filter((layer: LayerOption) => this.matchLayerByLogicalId(layer, layerIds));
    }

    return businessLayers;
  }

  /**
   * 通过逻辑ID匹配图层（忽略坐标系后缀）
   * @param layer 图层配置
   * @param layerIds 图层ID列表
   */
  private matchLayerByLogicalId(layer: LayerOption, layerIds: string[]): boolean {
    const logicalId = layer.id.replace(/_cgcs2000|_gcj02$/i, '');
    return layerIds.includes(logicalId);
  }

  /**
   * 坐标转换（支持 WGS84、CGCS2000、GCJ02 之间的转换）
   * @param coordinate 原始坐标 [经度, 纬度]
   * @param from 源坐标系
   * @param to 目标坐标系
   */
  transformCoordinate(coordinate: Coordinate, from: CoordinateSystem, to: CoordinateSystem): CoordinateTransformResult {
    // 如果坐标系相同，直接返回
    if (from === to) {
      return {
        coordinate,
        accuracy: 0,
        source: from,
        target: to
      };
    }

    // 生成缓存键
    const cacheKey = `${coordinate[0]},${coordinate[1]}_${from}_${to}`;

    // 检查缓存
    if (this.transformCache.has(cacheKey)) {
      return this.transformCache.get(cacheKey)!;
    }

    // 执行坐标转换
    let transformedCoordinate: Coordinate;
    let accuracy = 1; // 使用 coordtransform 库，精度更高，约1米误差

    try {
      transformedCoordinate = this.performCoordinateTransform(coordinate, from, to);
    } catch (error) {
      console.warn('🔄 坐标转换失败，返回原坐标:', error);
      transformedCoordinate = coordinate;
      accuracy = 0;
    }

    const result: CoordinateTransformResult = {
      coordinate: transformedCoordinate,
      accuracy,
      source: from,
      target: to
    };

    // 添加到缓存
    this.addToCache(cacheKey, result);

    return result;
  }

  /**
   * 批量坐标转换
   * @param coordinates 坐标数组
   * @param from 源坐标系
   * @param to 目标坐标系
   */
  batchTransformCoordinates(coordinates: Coordinate[], from: CoordinateSystem, to: CoordinateSystem): CoordinateTransformResult[] {
    return coordinates.map(coord => this.transformCoordinate(coord, from, to));
  }

  /**
   * 执行坐标转换（使用 coordtransform 库）
   * @param coordinate 原始坐标 [经度, 纬度]
   * @param from 源坐标系
   * @param to 目标坐标系
   */
  private performCoordinateTransform(coordinate: Coordinate, from: CoordinateSystem, to: CoordinateSystem): Coordinate {
    const [lng, lat] = coordinate;

    // WGS84 与 GCJ02 的直接转换
    if (from === CoordinateSystem.WGS84 && to === CoordinateSystem.GCJ02) {
      return coordtransform.wgs84togcj02(lng, lat);
    }
    if (from === CoordinateSystem.GCJ02 && to === CoordinateSystem.WGS84) {
      return coordtransform.gcj02towgs84(lng, lat);
    }

    // CGCS2000 与其他坐标系的转换（CGCS2000 接近 WGS84）
    if (from === CoordinateSystem.CGCS2000 && to === CoordinateSystem.GCJ02) {
      // CGCS2000 → WGS84 → GCJ02
      return coordtransform.wgs84togcj02(lng, lat);
    }
    if (from === CoordinateSystem.GCJ02 && to === CoordinateSystem.CGCS2000) {
      // GCJ02 → WGS84 → CGCS2000
      return coordtransform.gcj02towgs84(lng, lat);
    }
    if (from === CoordinateSystem.WGS84 && to === CoordinateSystem.CGCS2000) {
      // WGS84 与 CGCS2000 非常接近，直接返回
      return [lng, lat];
    }
    if (from === CoordinateSystem.CGCS2000 && to === CoordinateSystem.WGS84) {
      // CGCS2000 与 WGS84 非常接近，直接返回
      return [lng, lat];
    }

    // 如果没有匹配的转换，返回原坐标
    console.warn(`🔄 不支持的坐标转换: ${from} → ${to}`);
    return coordinate;
  }

  /**
   * 添加到缓存
   */
  private addToCache(key: string, result: CoordinateTransformResult): void {
    // 如果缓存已满，删除最旧的条目
    if (this.transformCache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.transformCache.keys().next().value;
      this.transformCache.delete(firstKey);
    }
    
    this.transformCache.set(key, result);
  }

  /**
   * 清空转换缓存
   */
  clearTransformCache(): void {
    this.transformCache.clear();
  }

  /**
   * 验证坐标有效性
   * @param coordinate 坐标
   */
  isValidCoordinate(coordinate: Coordinate): boolean {
    if (!coordinate || coordinate.length < 2) {
      return false;
    }
    
    const [lon, lat] = coordinate;
    
    // 检查经纬度范围（中国境内）
    return lon >= 73 && lon <= 135 && lat >= 18 && lat <= 54;
  }

  /**
   * 获取坐标系显示名称
   */
  getCoordinateSystemDisplayName(crs: CoordinateSystem): string {
    switch (crs) {
      case CoordinateSystem.WGS84:
        return 'WGS84';
      case CoordinateSystem.CGCS2000:
        return '国家2000';
      case CoordinateSystem.GCJ02:
        return '火星坐标';
      default:
        return '未知坐标系';
    }
  }




}
