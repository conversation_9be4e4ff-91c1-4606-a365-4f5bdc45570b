import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { Collection, Feature } from 'ol';
import { Map, View } from 'ol';
import { MapOptions } from 'ol/PluggableMap';
import { ViewOptions } from 'ol/View';
import { defaults as defaultControls } from 'ol/control';
import { Coordinate } from 'ol/coordinate';
import Geometry from 'ol/geom/Geometry';
import Point from 'ol/geom/Point';
import { circular as circularPolygon } from 'ol/geom/Polygon';
import BaseLayer from 'ol/layer/Base';
import LayerGroup from 'ol/layer/Group';
import TileLayer from 'ol/layer/Tile';
import VectorLayer from 'ol/layer/Vector';
import { TileWMS, XYZ } from 'ol/source';
import VectorSource from 'ol/source/Vector';
import { Circle as CircleStyle, Fill, Stroke, Style } from 'ol/style';
import { environment } from 'src/environments/environment';
import { ShareModuleService } from '../../share.service';
import { LayerOption } from './class/layer-option';
import { LayerConfigsCN } from './layer.config';
import { CoordinateSystemService } from '../service/coordinate-system.service';
import TileSource from 'ol/source/Tile';
import { DetailsMode } from 'src/app/@core/base/environment';
import { LocationSelectComponent } from '../location-select/location-select.component';
import { ModalController } from '@ionic/angular';
import { Extent } from 'ol/extent';
import { EvreportComponent } from 'src/app/execut/modal/evreport/evreport.component';
import { ExecutService } from 'src/app/execut/execut.service';
import { fromEvent, Subject } from 'rxjs';
import { takeUntil, throttleTime } from 'rxjs/operators';
import { CameraLayerComponent } from '../camera-layer/camera-layer.component';
import { MapService } from '../service';
import { KeyPointRenderer } from './key-point-renderer';
import { ToolsBarComponent } from './tools-bar/tools-bar.component';

@Component({
  selector: 'ost-map',
  templateUrl: './map.component.html',
  styleUrls: ['./map.component.scss']
})
export class MapComponent implements OnInit, OnDestroy {
  @ViewChild('ostMap', { static: false }) mapElement: ElementRef<HTMLDivElement>;
  @ViewChild('toolsBar', { static: false }) public toolsBar: ToolsBarComponent;
  @Input() taskCode: string;
  @Input() center: Coordinate = [112.629150, 35.553938]; // 地图中心
  @Input() zoom = 14; // 初始比例尺
  @Input() projection = 'EPSG:4326'; // 空间参考系
  @Input() addMapClickEvent = false; // 是否添加地图点击事件
  @Input() layerIds: string[]; // 需要操作的业务图层ID列表
  @Input() showLocationInfo = false; // 是否显示位置信息小工具
  @Input() showLocationProviderButton = false; // 是否显示定位模式选择按钮
  @Input() inspectionMethod: string; // 巡检方式
  @Output() mapLoaded = new EventEmitter<void>(); // 地图加载完成事件
  public map: Map; // 地图对象
  public view: View; // 视图对象
  public baseLayer: LayerGroup = new LayerGroup(); // 基础图层
  public businessLayer: LayerGroup = new LayerGroup(); // 业务图层
  public baseLayerList: Collection<LayerGroup> = new Collection(); // 基础图层集合
  public businessLayerList: Collection<BaseLayer> = new Collection(); // 业务图层集合
  public locationLayer: VectorLayer<VectorSource<Geometry>> = new VectorLayer({ source: new VectorSource() }); // 定位图层
  public currentPositionFeature: Feature<Point> = new Feature(new Point([])); // 当前位置要素
  public currentPositionExtentFeature: Feature<Geometry> = new Feature(); // 当前位置范围要素

  // 位置信息显示
  public currentLocationInfo = {
    longitude: 0,
    latitude: 0,
    accuracy: 0
  };

  // 是否显示刷新图标
  public showRefreshIcon = false;

  // 新增关键点图层
  private keyPointLayer: VectorLayer<VectorSource<Geometry>> = null;

  // 用于跟踪当前打开的 CameraLayerComponent 模态框实例
  private currentCameraModalInstance: HTMLIonModalElement | null = null;
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  // 在类内添加属性
  private _lastKeyPoints: any[] = [];
  constructor(
    public netSer: ShareModuleService, private modalCtrl: ModalController,
    private exeSer: ExecutService, private mapService: MapService,
    private coordinateSystemService: CoordinateSystemService
  ) { }

  ngOnInit(): void {
    this.initBaseLayers(); // 初始化基础图层
    this.initBusinessLayers(this.layerIds); // 初始化业务图层
    this.initMap(); // 初始化地图
    this.initBaseFeature(); // 初始化基础要素
    // 注册地图组件实例到服务中
    this.mapService.registerMapComponent(this);

    // 监听坐标系变化，重新加载业务图层
    this.coordinateSystemService.getCurrentCoordinateSystem()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.reloadBusinessLayers();
      });
  }

  /**
   * 初始化地图
   */
  private initMap(): void {
    const viewOptions: ViewOptions = {
      center: environment.openProxy ? [116.18220449613645, 40.06772521388271] : this.center,
      zoom: this.zoom,
      maxZoom: 18,
      projection: this.projection,
      constrainRotation: false,
      enableRotation: false,
      constrainResolution: false
    };

    this.view = new View(viewOptions);
    this.locationLayer.set('serverType', 'locationLayer');
    this.baseLayer.set('serverType', 'baseLayerGroup');

    const mapOptions: MapOptions = {
      layers: [this.baseLayer, this.businessLayer, this.locationLayer],
      view: this.view,
      controls: defaultControls({ attribution: false, rotate: false, zoom: false }),
    };

    this.map = new Map(mapOptions);
    setTimeout(() => {
      this.map.setTarget(this.mapElement.nativeElement);
      if (this.addMapClickEvent) {
        this.setupMapClickHandler(); // 设置地图点击事件处理
      }
      // 地图初始化完成后触发加载完成事件
      this.mapLoaded.emit();
    }, 250);
  }

  /**
   * 设置地图点击事件处理
   */
  private setupMapClickHandler() {
    this.map.un('click', this.mapClickHandler); // 解绑先前的点击事件监听器
    // 绑定新的点击事件监听器
    // 使用 RxJS fromEvent 创建点击事件流
    fromEvent(this.map, 'click').pipe(
      // 使用 throttleTime 操作符进行节流，1秒内最多触发一次
      throttleTime(2000),
      // 在组件销毁时自动取消订阅
      takeUntil(this.destroy$)
    ).subscribe((evt: any) => this.mapClickHandler(evt));
  }

  /**
   * 地图点击事件处理器
   * @param evt - 点击事件对象
   */
  private mapClickHandler = (evt) => {
    this.fetchFeatureAtCoordinate(evt.coordinate); // 根据点击坐标获取要素信息
  };

  /**
   * 初始化基础图层
   */
  private initBaseLayers(): void {
    const layers = LayerConfigsCN;
    layers.forEach(layer => {
      const childLayers = layer.childs.map(childlayer => this.createLayer(childlayer));
      const layerGroup = new LayerGroup({ layers: childLayers });
      this.setLayerProperties(layerGroup, layer);
      this.baseLayerList.push(layerGroup);
    });

    const defaultLayer = this.baseLayerList.getArray().find(item => item.get('selected'));
    if (defaultLayer) {
      this.baseLayer.setLayers(defaultLayer.getLayers()); // 设置默认基础图层
    }
  }

  /**
   * 初始化业务图层
   */
  private initBusinessLayers(layerIds?: string[]): void {
    // 使用坐标系服务获取当前坐标系对应的业务图层配置
    const layers = this.coordinateSystemService.getCurrentBusinessLayers(layerIds);

    this.businessLayerList.clear();
    this.businessLayerList.extend(layers.map(layerConfig => {
      const layer = this.createLayer(layerConfig);
      this.setLayerProperties(layer, layerConfig);
      return layer;
    }));
    this.businessLayer.setLayers(this.businessLayerList); // 设置业务图层
  }

  /**
   * 重新加载业务图层（坐标系切换时调用）
   */
  private reloadBusinessLayers(): void {
    // 清除现有业务图层
    this.businessLayerList.clear();

    // 重新初始化业务图层
    this.initBusinessLayers(this.layerIds);

    console.log('🔄 业务图层已根据新坐标系重新加载');
  }
  /**
   * 创建图层
   * @param layer - 图层配置项
   * @returns {BaseLayer} 创建的图层
   */
  private createLayer(layer: LayerOption): BaseLayer {
    let sourceOptions: any = {
      url: layer.url,
      params: { ...layer.param }
    };

    if (this.taskCode) {
      sourceOptions.params.taskCode = this.taskCode;
    }

    // 如果是关键点图层且巡检方式为"巡视"，添加CQL_FILTER参数
    if (layer.id === 'inspect_point' && this.inspectionMethod) {
      sourceOptions.params.CQL_FILTER = `inspectionMethod='${this.inspectionMethod}'`;
    }

    switch (layer.serverType) {
      case 'TILE':
        return new TileLayer({ source: new XYZ(sourceOptions), extent: layer.extent });
      case 'WMS':
        return new TileLayer({ source: new TileWMS(sourceOptions), extent: layer.extent });
      default:
        console.error('无法解析' + layer.id + '类型服务');
        return null;
    }
  }

  /**
   * 设置图层属性
   * @param layer - 图层对象
   * @param config - 图层配置
   */
  private setLayerProperties(layer: BaseLayer, config: any): void {
    layer.set('id', config.id);
    layer.set('name', config.name);
    layer.set('icon', config.icon);
    layer.set('serverType', config.serverType);
    layer.set('selected', config.selected);
  }
  /**
   * 初始化基础要素
   */
  private initBaseFeature(): void {
    this.createCurrentPosition(); // 创建当前位置要素
    this.createCurrentPositionExtent(); // 创建当前位置范围要素
    this.showRefreshIcon = false; // 初始化时隐藏刷新图标
  }
  /**
   * 设置当前位置
   * @param coordinate - 坐标
   * @param accuracy - 精度
   * @param followView - 是否跟随视图
   */
  public setCurrentLocation(coordinate: Coordinate, accuracy: number = 0, followView: boolean = true): void {
    // 验证坐标是否有效
    if (!coordinate || !Array.isArray(coordinate) || coordinate.length < 2) {
      console.warn('setCurrentLocation: 无效的坐标', coordinate);
      return;
    }

    // 验证坐标值是否为有效数字
    if (!Number.isFinite(coordinate[0]) || !Number.isFinite(coordinate[1])) {
      console.warn('setCurrentLocation: 坐标包含无效数值', coordinate);
      return;
    }

    try {
      // 更新位置信息显示
      this.currentLocationInfo = {
        longitude: Number(coordinate[0].toFixed(6)),
        latitude: Number(coordinate[1].toFixed(6)),
        accuracy: Math.round(accuracy || 0)
      };

      // 设置当前位置点
      this.currentPositionFeature.getGeometry().setCoordinates(coordinate);

      // 确保精度值有效，最小值为1米，避免创建无效的圆形多边形
      const validAccuracy = Math.max(accuracy || 1, 1);

      // 创建圆形多边形几何体
      const circularGeometry = circularPolygon(coordinate, validAccuracy);
      this.currentPositionExtentFeature.setGeometry(circularGeometry);

      // 如果有有效的定位结果，显示刷新图标
      if (accuracy > 0 && this.showLocationInfo) {
        this.showRefreshIcon = true;
      }

      // 如果需要跟随视图，验证范围后再调用fit
      if (followView) {
        const extent = circularGeometry.getExtent();

        // 验证范围是否有效
        if (extent && extent.length === 4 &&
          Number.isFinite(extent[0]) && Number.isFinite(extent[1]) &&
          Number.isFinite(extent[2]) && Number.isFinite(extent[3]) &&
          extent[0] < extent[2] && extent[1] < extent[3]) {
          this.view.fit(extent, { duration: 500, maxZoom: 16, padding: [50, 50, 50, 50] });
        } else {
          console.warn('setCurrentLocation: 无效的几何体范围，跳过视图适配', extent);
          // 作为备选方案，直接设置视图中心
          this.view.setCenter(coordinate);
        }
      }
    } catch (error) {
      console.error('setCurrentLocation: 设置当前位置时发生错误', error, { coordinate, accuracy, followView });

      // 错误恢复：至少尝试设置视图中心
      try {
        this.view.setCenter(coordinate);
      } catch (centerError) {
        console.error('setCurrentLocation: 设置视图中心也失败', centerError);
      }
    }
  }
  /**
   * 创建当前位置要素
   */
  private createCurrentPosition(): void {
    const currentPosit = new Style({
      image: new CircleStyle({
        fill: new Fill({ color: '#3dc2ff' }),
        stroke: new Stroke({ color: '#FFFFFF', width: 2 }),
        radius: 8,
      })
    });
    this.currentPositionFeature.setStyle(currentPosit);
    this.locationLayer.getSource().addFeature(this.currentPositionFeature);
  }

  /**
   * 创建当前位置范围要素
   */
  private createCurrentPositionExtent(): void {
    const currentPositExtent = new Style({
      fill: new Fill({ color: 'rgba(16,94,169,0.1)' }),
      stroke: new Stroke({ color: '#FFFFFF', width: 2 }),
    });
    this.currentPositionExtentFeature.setStyle(currentPositExtent);
    this.locationLayer.getSource().addFeature(this.currentPositionExtentFeature);
  }



  /**
   * 批量渲染关键点及其范围
   * @param points 关键点数组，包含坐标和状态
   */
  public setKeyPoints(points: any[]): void {
    if (!this.keyPointLayer) {
      this.keyPointLayer = new VectorLayer({
        source: new VectorSource(),
        zIndex: 100 // 保证在WMS图层之上
      });
      this.map.addLayer(this.keyPointLayer);
      // 只需setup一次
      KeyPointRenderer.setupAutoRefresh(this.keyPointLayer, this.view, () => this._lastKeyPoints);
    }
    KeyPointRenderer.renderKeyPoints(this.keyPointLayer, points, this.view);
    this._lastKeyPoints = points;
  }

  /**
   * 更新单个关键点的状态（变色）
   * @param id 关键点ID
   * @param status '已巡' | '未巡'
   */
  public updateKeyPointStatus(id: string, status: '已巡' | '未巡'): void {
    KeyPointRenderer.updateKeyPointStatus(this.keyPointLayer, this._lastKeyPoints, id, status, this.view);
  }

  /**
   * 根据坐标获取业务图层上的要素信息
   * @param coordinate - 用户点击的坐标
   */
  async fetchFeatureAtCoordinate(coordinate: Coordinate): Promise<void> {
    const busLayerArray = this.businessLayerList.getArray();
    for (const item of busLayerArray) {
      if (this.layerIds.includes(item.get('id'))) {
        const layerSource = (item as TileLayer<TileSource>).getSource() as TileWMS;
        const url = layerSource.getFeatureInfoUrl(coordinate, this.map.getView().getResolution() as number * 10, 'EPSG:4326', { INFO_FORMAT: 'application/json', FEATURE_COUNT: 1 }) || '';
        if (url) {
          try {
            const response = await fetch(url);
            if (!response.ok) throw new Error('Network response was not ok');
            const data = await response.json();
            if (data.features.length > 0) {
              const feature = data.features[0];
              const geometryName = feature.geometry_name;
              const modelInfo = geometryName === 'geom' ? { ...feature.properties } : { ...feature.properties, pointGeom: JSON.parse(feature.properties.geom).coordinates };
              switch (geometryName) {
                case 'pointGeom':
                  await this.openKeyPoint(modelInfo);
                  break;
                case 'eventGeom':
                  this.getEventInfoByCode(modelInfo.eventCode);
                  break;
                case 'geom':
                  // 处理摄像头图层点击
                  this.openCameraModal(modelInfo);
                  break;
              }
              break; // 在处理第一个匹配特性后退出循环
            }
          } catch (error) {
            console.error('Error fetching feature info:', error);
          }
        }
      }
    }
  }

  /**
   * 打开关键点修改弹窗
   * @param modelInfo - 关键点信息
   */
  async openKeyPoint(modelInfo): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: LocationSelectComponent,
      componentProps: { modelInfo, modelMode: DetailsMode.EDITE },
      cssClass: 'app-location-select',
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role === 'confirm') {
      this.refreshLayer({ layerId: 'inspect_point', coordinate: data.centerCoord });
    }
  }

  /**
   * 根据事件编码获取事件信息
   * @param eventCode - 事件编码
   */
  getEventInfoByCode(eventCode: string): void {
    this.exeSer.getEReportByCode({ eventCode })
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        if (res.code === 0) {
          const basicInfo = res.data;
          basicInfo.eventGeom = JSON.parse(basicInfo.eventGeom).coordinates;
          this.openEventReport(basicInfo);
        }
      });
  }

  /**
   * 打开事件上报
   */
  async openEventReport(modelInfo): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: EvreportComponent,
      componentProps: { modelInfo, modelMode: DetailsMode.EDITE },
      cssClass: 'app-evreport',
    });
    await modal.present();
    // const { data, role } = await modal.onWillDismiss();
    // if (role === 'confirm') {
    //   this.refreshLayer({ layerId: 'inspect_event', coordinate: data.eventGeom });
    // }
  }

  /**
   * 打开摄像头图层模态框
   * @param modelInfo 包含摄像头ID的模型信息
   */
  async openCameraModal(modelInfo: { id: string }): Promise<void> {
    // 检查并处理已存在的模态框实例
    if (this.currentCameraModalInstance) {
      const existingModelInfo = this.currentCameraModalInstance.componentProps?.modelInfo;

      if (existingModelInfo?.id === modelInfo.id) {
        return; // 避免重复打开相同ID的模态框
      }

      await this.currentCameraModalInstance.dismiss();
      this.currentCameraModalInstance = null;
    }

    // 创建并配置新的模态框
    const newModal = await this.modalCtrl.create({
      component: CameraLayerComponent,
      componentProps: { modelInfo },
      showBackdrop: false,
      cssClass: ['app-camera-layer', 'allow-events-through'],
    });

    // 设置模态框实例并监听关闭事件
    this.currentCameraModalInstance = newModal;
    newModal.onWillDismiss().then(() => {
      if (this.currentCameraModalInstance === newModal) {
        this.currentCameraModalInstance = null;
      }
    });

    await newModal.present();
  }

  /**
   * 触发定位功能
   */
  public triggerLocation(): void {
    try {
      if (this.toolsBar) {
        this.toolsBar.onLocation();
      }
    } catch (error) {
      console.error('触发定位功能失败:', error);
    }
  }

  /**
   * 刷新定位
   */
  public onRefreshLocation(): void {
    this.triggerLocation();
  }

  /**
   * 设置刷新图标显示状态
   * @param show - 是否显示刷新图标
   */
  public setRefreshIconVisibility(show: boolean): void {
    this.showRefreshIcon = show && this.showLocationInfo;
  }

  /**
   * 刷新图层
   * @param data - 刷新图层所需的数据
   */
  refreshLayer(data) {
    const { layerId, coordinate } = data;
    this.refreshBusinessLayer(layerId, coordinate);
  }

  /**
   * 获取当前视图的可视范围
   * @returns {Extent} 可视范围
   */
  private getVisibleExtent(): Extent {
    return this.map.getView().calculateExtent(this.map.getSize());
  }

  /**
   * 刷新指定ID的业务图层并将视图中心设置为给定坐标
   * @param layerId 图层ID
   * @param coordinate 坐标
   */
  refreshBusinessLayer(layerId: string, coordinate?: Coordinate): void {
    // 使用坐标系服务获取当前坐标系对应的业务图层配置
    const layers = this.coordinateSystemService.getCurrentBusinessLayers();
    const layerConfig = layers.find(layer => {
      // 支持通过逻辑ID匹配（忽略坐标系后缀）
      const logicalId = layer.id.replace(/_cgcs2000|_gcj02|_wgs84$/i, '');
      return layer.id === layerId || logicalId === layerId;
    });

    if (layerConfig) {
      const refreshedLayer = this.createLayer(layerConfig);
      this.setLayerProperties(refreshedLayer, layerConfig);

      const layersArray = this.businessLayer.getLayers().getArray();
      const index = layersArray.findIndex(layer => layer.get('id') === layerId);

      // 获取可视范围
      const visibleExtent = this.getVisibleExtent();
      if (refreshedLayer instanceof TileLayer && refreshedLayer.getSource() instanceof TileWMS) {
        const tileSource = refreshedLayer.getSource() as TileWMS;
        const currentParams = tileSource.getParams();
        currentParams.CRS = this.projection;
        currentParams.BBOX = visibleExtent.join(',');
        tileSource.updateParams(currentParams);
        tileSource.refresh(); // 强制刷新图层
      }

      // 更新或添加图层到业务图层集合中
      if (index !== -1) {
        layersArray[index] = refreshedLayer;
      } else {
        layersArray.push(refreshedLayer);
      }

      // 如果有指定坐标，设置中心
      if (coordinate) {
        this.view.setCenter(coordinate);
        this.view.animate({ center: coordinate, duration: 500 });
      }
    } else {
      console.error('Layer with ID ' + layerId + ' not found');
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
    // 注销地图组件实例
    this.mapService.unregisterMapComponent();
  }

}
